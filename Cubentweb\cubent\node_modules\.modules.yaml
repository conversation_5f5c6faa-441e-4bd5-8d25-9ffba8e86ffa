hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ark/schema@0.46.0':
    '@ark/schema': private
  '@ark/util@0.46.0':
    '@ark/util': private
  '@asamuzakjp/css-color@2.8.3':
    '@asamuzakjp/css-color': private
  '@auto-it/bot-list@11.3.0':
    '@auto-it/bot-list': private
  '@auto-it/core@11.3.0(@types/node@22.15.21)(typescript@5.8.3)':
    '@auto-it/core': private
  '@babel/code-frame@7.26.2':
    '@babel/code-frame': private
  '@babel/compat-data@7.26.8':
    '@babel/compat-data': private
  '@babel/core@7.26.9':
    '@babel/core': private
  '@babel/generator@7.26.9':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.26.5':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.25.9':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.9)':
    '@babel/helper-module-transforms': private
  '@babel/helper-string-parser@7.25.9':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.25.9':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.25.9':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.26.9':
    '@babel/helpers': private
  '@babel/parser@7.26.9':
    '@babel/parser': private
  '@babel/runtime-corejs3@7.26.9':
    '@babel/runtime-corejs3': private
  '@babel/runtime@7.26.9':
    '@babel/runtime': private
  '@babel/template@7.26.9':
    '@babel/template': private
  '@babel/traverse@7.26.9':
    '@babel/traverse': private
  '@babel/types@7.26.9':
    '@babel/types': private
  '@biomejs/cli-darwin-arm64@1.9.4':
    '@biomejs/cli-darwin-arm64': private
  '@biomejs/cli-darwin-x64@1.9.4':
    '@biomejs/cli-darwin-x64': private
  '@biomejs/cli-linux-arm64-musl@1.9.4':
    '@biomejs/cli-linux-arm64-musl': private
  '@biomejs/cli-linux-arm64@1.9.4':
    '@biomejs/cli-linux-arm64': private
  '@biomejs/cli-linux-x64-musl@1.9.4':
    '@biomejs/cli-linux-x64-musl': private
  '@biomejs/cli-linux-x64@1.9.4':
    '@biomejs/cli-linux-x64': private
  '@biomejs/cli-win32-arm64@1.9.4':
    '@biomejs/cli-win32-arm64': private
  '@biomejs/cli-win32-x64@1.9.4':
    '@biomejs/cli-win32-x64': private
  '@clack/core@0.5.0':
    '@clack/core': private
  '@clerk/backend@1.33.0(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(svix@1.66.0)':
    '@clerk/backend': private
  '@clerk/clerk-react@5.31.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/clerk-react': private
  '@clerk/nextjs@6.20.0(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(svix@1.66.0)':
    '@clerk/nextjs': private
  '@clerk/shared@3.9.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@clerk/shared': private
  '@clerk/types@4.59.1':
    '@clerk/types': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@csstools/color-helpers@5.0.2':
    '@csstools/color-helpers': private
  '@csstools/css-calc@2.1.2(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-calc': private
  '@csstools/css-color-parser@3.0.8(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-color-parser': private
  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    '@csstools/css-parser-algorithms': private
  '@csstools/css-tokenizer@3.0.3':
    '@csstools/css-tokenizer': private
  '@endemolshinegroup/cosmiconfig-typescript-loader@3.0.2(cosmiconfig@7.0.0)(typescript@5.8.3)':
    '@endemolshinegroup/cosmiconfig-typescript-loader': private
  '@esbuild/aix-ppc64@0.25.2':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.2':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.2':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.2':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.2':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.2':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.2':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.2':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.2':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.2':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.2':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.2':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.2':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.2':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.2':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.2':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.2':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.2':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.2':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.2':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.2':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.2':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.2':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.2':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.2':
    '@esbuild/win32-x64': private
  '@img/sharp-darwin-arm64@0.34.2':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.2':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.2':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.2':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.2':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.2':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.2':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.2':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.2':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-arm64@0.34.2':
    '@img/sharp-win32-arm64': private
  '@img/sharp-win32-ia32@0.34.2':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.2':
    '@img/sharp-win32-x64': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@next/env@15.3.2':
    '@next/env': private
  '@next/swc-darwin-arm64@15.3.2':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.2':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.2':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.2':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.2':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.2':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.2':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.2':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@octokit/auth-token@2.5.0':
    '@octokit/auth-token': private
  '@octokit/core@3.6.0':
    '@octokit/core': private
  '@octokit/endpoint@6.0.12':
    '@octokit/endpoint': private
  '@octokit/graphql@4.8.0':
    '@octokit/graphql': private
  '@octokit/openapi-types@12.11.0':
    '@octokit/openapi-types': private
  '@octokit/plugin-enterprise-compatibility@1.3.0':
    '@octokit/plugin-enterprise-compatibility': private
  '@octokit/plugin-paginate-rest@2.21.3(@octokit/core@3.6.0)':
    '@octokit/plugin-paginate-rest': private
  '@octokit/plugin-request-log@1.0.4(@octokit/core@3.6.0)':
    '@octokit/plugin-request-log': private
  '@octokit/plugin-rest-endpoint-methods@5.16.2(@octokit/core@3.6.0)':
    '@octokit/plugin-rest-endpoint-methods': private
  '@octokit/plugin-retry@3.0.9':
    '@octokit/plugin-retry': private
  '@octokit/plugin-throttling@3.7.0(@octokit/core@3.6.0)':
    '@octokit/plugin-throttling': private
  '@octokit/request-error@2.1.0':
    '@octokit/request-error': private
  '@octokit/request@5.6.3':
    '@octokit/request': private
  '@octokit/rest@18.12.0':
    '@octokit/rest': private
  '@octokit/types@6.41.0':
    '@octokit/types': private
  '@opentelemetry/api-logs@0.57.2':
    '@opentelemetry/api-logs': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/context-async-hooks': private
  '@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/core': private
  '@opentelemetry/instrumentation-amqplib@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-amqplib': private
  '@opentelemetry/instrumentation-connect@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-connect': private
  '@opentelemetry/instrumentation-dataloader@0.16.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-dataloader': private
  '@opentelemetry/instrumentation-express@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-express': private
  '@opentelemetry/instrumentation-fs@0.19.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-fs': private
  '@opentelemetry/instrumentation-generic-pool@0.43.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-generic-pool': private
  '@opentelemetry/instrumentation-graphql@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-graphql': private
  '@opentelemetry/instrumentation-hapi@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-hapi': private
  '@opentelemetry/instrumentation-http@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-http': private
  '@opentelemetry/instrumentation-ioredis@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-ioredis': private
  '@opentelemetry/instrumentation-kafkajs@0.7.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-kafkajs': private
  '@opentelemetry/instrumentation-knex@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-knex': private
  '@opentelemetry/instrumentation-koa@0.47.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-koa': private
  '@opentelemetry/instrumentation-lru-memoizer@0.44.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-lru-memoizer': private
  '@opentelemetry/instrumentation-mongodb@0.52.0(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongodb': private
  '@opentelemetry/instrumentation-mongoose@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mongoose': private
  '@opentelemetry/instrumentation-mysql2@0.45.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql2': private
  '@opentelemetry/instrumentation-mysql@0.45.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-mysql': private
  '@opentelemetry/instrumentation-pg@0.51.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-pg': private
  '@opentelemetry/instrumentation-redis-4@0.46.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-redis-4': private
  '@opentelemetry/instrumentation-tedious@0.18.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-tedious': private
  '@opentelemetry/instrumentation-undici@0.10.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation-undici': private
  '@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0)':
    '@opentelemetry/instrumentation': private
  '@opentelemetry/redis-common@0.36.2':
    '@opentelemetry/redis-common': private
  '@opentelemetry/resources@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/resources': private
  '@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sdk-trace-base': private
  '@opentelemetry/semantic-conventions@1.30.0':
    '@opentelemetry/semantic-conventions': private
  '@opentelemetry/sql-common@0.40.1(@opentelemetry/api@1.9.0)':
    '@opentelemetry/sql-common': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@prisma/instrumentation@6.7.0(@opentelemetry/api@1.9.0)':
    '@prisma/instrumentation': private
  '@rollup/plugin-commonjs@28.0.1(rollup@4.35.0)':
    '@rollup/plugin-commonjs': private
  '@rollup/pluginutils@5.1.4(rollup@4.35.0)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.35.0':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.35.0':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.35.0':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.35.0':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.35.0':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.35.0':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.35.0':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.35.0':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.35.0':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.35.0':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.35.0':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.35.0':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.35.0':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.38.0':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.35.0':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.35.0':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.35.0':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.35.0':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.35.0':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.35.0':
    '@rollup/rollup-win32-x64-msvc': private
  '@sentry-internal/browser-utils@9.22.0':
    '@sentry-internal/browser-utils': private
  '@sentry-internal/feedback@9.22.0':
    '@sentry-internal/feedback': private
  '@sentry-internal/replay-canvas@9.22.0':
    '@sentry-internal/replay-canvas': private
  '@sentry-internal/replay@9.22.0':
    '@sentry-internal/replay': private
  '@sentry/babel-plugin-component-annotate@3.3.1':
    '@sentry/babel-plugin-component-annotate': private
  '@sentry/browser@9.22.0':
    '@sentry/browser': private
  '@sentry/bundler-plugin-core@3.3.1':
    '@sentry/bundler-plugin-core': private
  '@sentry/cli-darwin@2.42.2':
    '@sentry/cli-darwin': private
  '@sentry/cli-linux-arm64@2.42.2':
    '@sentry/cli-linux-arm64': private
  '@sentry/cli-linux-arm@2.42.2':
    '@sentry/cli-linux-arm': private
  '@sentry/cli-linux-i686@2.42.2':
    '@sentry/cli-linux-i686': private
  '@sentry/cli-linux-x64@2.42.2':
    '@sentry/cli-linux-x64': private
  '@sentry/cli-win32-i686@2.42.2':
    '@sentry/cli-win32-i686': private
  '@sentry/cli-win32-x64@2.42.2':
    '@sentry/cli-win32-x64': private
  '@sentry/cli@2.42.2':
    '@sentry/cli': private
  '@sentry/core@9.22.0':
    '@sentry/core': private
  '@sentry/nextjs@9.22.0(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(next@15.3.2(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(react@19.1.0)(webpack@5.98.0(esbuild@0.25.2))':
    '@sentry/nextjs': private
  '@sentry/node@9.22.0':
    '@sentry/node': private
  '@sentry/opentelemetry@9.22.0(@opentelemetry/api@1.9.0)(@opentelemetry/context-async-hooks@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/core@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/instrumentation@0.57.2(@opentelemetry/api@1.9.0))(@opentelemetry/sdk-trace-base@1.30.1(@opentelemetry/api@1.9.0))(@opentelemetry/semantic-conventions@1.30.0)':
    '@sentry/opentelemetry': private
  '@sentry/react@9.22.0(react@19.1.0)':
    '@sentry/react': private
  '@sentry/vercel-edge@9.22.0':
    '@sentry/vercel-edge': private
  '@sentry/webpack-plugin@3.3.1(webpack@5.98.0(esbuild@0.25.2))':
    '@sentry/webpack-plugin': private
  '@stablelib/base64@1.0.1':
    '@stablelib/base64': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@t3-oss/env-core@0.13.4(arktype@2.1.20)(typescript@5.8.3)(zod@3.25.28)':
    '@t3-oss/env-core': private
  '@t3-oss/env-nextjs@0.13.4(arktype@2.1.20)(typescript@5.8.3)(zod@3.25.28)':
    '@t3-oss/env-nextjs': private
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': private
  '@testing-library/react@16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@testing-library/react': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@turbo/workspaces@2.5.3':
    '@turbo/workspaces': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/inquirer@6.5.0':
    '@types/inquirer': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/mysql@2.15.26':
    '@types/mysql': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/pg-pool@2.0.6':
    '@types/pg-pool': private
  '@types/pg@8.6.1':
    '@types/pg': private
  '@types/react-dom@19.1.5(@types/react@19.1.5)':
    '@types/react-dom': private
  '@types/react@19.1.5':
    '@types/react': private
  '@types/shimmer@1.2.0':
    '@types/shimmer': private
  '@types/tedious@4.0.14':
    '@types/tedious': private
  '@types/through@0.0.33':
    '@types/through': private
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': private
  '@vitest/expect@3.1.4':
    '@vitest/expect': private
  '@vitest/mocker@3.1.4(vite@6.2.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.39.0)(yaml@2.7.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.1.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.1.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.1.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.1.4':
    '@vitest/spy': private
  '@vitest/utils@3.1.4':
    '@vitest/utils': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  acorn-import-attributes@1.9.5(acorn@8.14.1):
    acorn-import-attributes: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@8.17.1:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  apps/api:
    api: private
  apps/app:
    app: private
  apps/docs:
    docs: private
  apps/email:
    email: private
  apps/storybook:
    storybook: private
  apps/studio:
    studio: private
  apps/web:
    web: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-query@5.3.0:
    aria-query: private
  arktype@2.1.20:
    arktype: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-union@2.1.0:
    array-union: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types@0.13.4:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  author-regex@1.0.0:
    author-regex: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  await-to-js@3.0.0:
    await-to-js: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  before-after-hook@2.2.3:
    before-after-hook: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  bottleneck@2.19.5:
    bottleneck: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bufferutil@4.0.9:
    bufferutil: private
  bundle-require@5.1.0(esbuild@0.25.2):
    bundle-require: private
  busboy@1.6.0:
    busboy: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@3.0.0:
    camel-case: private
  caniuse-lite@1.0.30001702:
    caniuse-lite: private
  chai@5.2.0:
    chai: private
  chalk@3.0.0:
    chalk: private
  change-case@3.1.0:
    change-case: private
  chardet@0.7.0:
    chardet: private
  check-error@2.1.1:
    check-error: private
  chokidar@4.0.3:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@3.0.0:
    cli-width: private
  client-only@0.0.1:
    client-only: private
  clone@1.0.4:
    clone: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  commondir@1.0.1:
    commondir: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.0:
    consola: private
  constant-case@2.0.0:
    constant-case: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@1.0.2:
    cookie: private
  core-js-pure@3.41.0:
    core-js-pure: private
  cosmiconfig@7.0.0:
    cosmiconfig: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssstyle@4.2.1:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-urls@5.0.0:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  decimal.js@10.5.0:
    decimal.js: private
  dedent@0.7.0:
    dedent: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-extend@0.6.0:
    deep-extend: private
  deepmerge@4.3.1:
    deepmerge: private
  defaults@1.0.4:
    defaults: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  del@5.1.0:
    del: private
  deprecation@2.3.1:
    deprecation: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.4:
    detect-libc: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  dom-accessibility-api@0.5.16:
    dom-accessibility-api: private
  dot-case@2.1.1:
    dot-case: private
  dotenv@8.6.0:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  electron-to-chromium@1.5.112:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  endent@2.1.0:
    endent: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  enquirer@2.4.1:
    enquirer: private
  entities@4.5.0:
    entities: private
  env-ci@5.5.0:
    env-ci: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es6-promise@4.2.8:
    es6-promise: private
  esbuild@0.25.2:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@1.0.5:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-scope@5.1.1:
    eslint-scope: private
  esprima@4.0.1:
    esprima: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  events@3.3.0:
    events: private
  execa@5.1.1:
    execa: private
  expect-type@1.2.1:
    expect-type: private
  external-editor@3.1.0:
    external-editor: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-parse@1.0.3:
    fast-json-parse: private
  fast-sha256@1.3.0:
    fast-sha256: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  figures@3.2.0:
    figures: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  forwarded-parse@2.1.2:
    forwarded-parse: private
  fp-ts@2.16.9:
    fp-ts: private
  fromentries@1.3.2:
    fromentries: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  fuse.js@7.1.0:
    fuse.js: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-uri@6.0.4:
    get-uri: private
  gitlog@4.0.8:
    gitlog: private
  glob-parent@5.1.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  globals@11.12.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@10.0.2:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gradient-string@2.0.2:
    gradient-string: private
  handlebars@4.7.8:
    handlebars: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  header-case@1.0.1:
    header-case: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  html-encoding-sniffer@4.0.0:
    html-encoding-sniffer: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-cwd@3.0.0:
    import-cwd: private
  import-fresh@3.3.1:
    import-fresh: private
  import-from@3.0.0:
    import-from: private
  import-in-the-middle@1.13.2:
    import-in-the-middle: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@8.2.6:
    inquirer: private
  internal-slot@1.1.0:
    internal-slot: private
  io-ts@2.2.22(fp-ts@2.16.9):
    io-ts: private
  ip-address@9.0.5:
    ip-address: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-interactive@1.0.0:
    is-interactive: private
  is-lower-case@1.1.3:
    is-lower-case: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-cwd@2.2.0:
    is-path-cwd: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  is-upper-case@1.1.2:
    is-upper-case: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isbinaryfile@4.0.10:
    isbinaryfile: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  java-properties@1.0.2:
    java-properties: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  jose@6.0.11:
    jose: private
  joycon@3.1.1:
    joycon: private
  js-cookie@3.0.5:
    js-cookie: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsdom@26.1.0(bufferutil@4.0.9):
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-parse-better-errors@1.0.2:
    json-parse-better-errors: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@1.0.0:
    json-schema-traverse: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  lightningcss-darwin-arm64@1.30.1:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.30.1:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.30.1:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.30.1:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.30.1:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.30.1:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.30.1:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.30.1:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.30.1:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.30.1:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.30.1:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-json-file@4.0.0:
    load-json-file: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash.chunk@4.2.0:
    lodash.chunk: private
  lodash.get@4.4.2:
    lodash.get: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  loupe@3.1.3:
    loupe: private
  lower-case-first@1.0.2:
    lower-case-first: private
  lower-case@1.1.4:
    lower-case: private
  lru-cache@7.18.3:
    lru-cache: private
  lucide-react@0.511.0(react@19.1.0):
    lucide-react: private
  lz-string@1.5.0:
    lz-string: private
  magic-string@0.30.17:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  map-obj@4.3.0:
    map-obj: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  mkdirp@0.5.6:
    mkdirp: private
  mlly@1.7.4:
    mlly: private
  module-details-from-path@1.0.3:
    module-details-from-path: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.8:
    nanoid: private
  neo-async@2.6.2:
    neo-async: private
  nested-error-stacks@2.0.1:
    nested-error-stacks: private
  netmask@2.0.2:
    netmask: private
  next-themes@0.4.6(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next-themes: private
  next@15.3.2(@babel/core@7.26.9)(@opentelemetry/api@1.9.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    next: private
  no-case@2.3.2:
    no-case: private
  node-fetch@2.6.7:
    node-fetch: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-plop@0.26.3:
    node-plop: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nwsapi@2.2.18:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  objectorarray@1.0.5:
    objectorarray: private
  obuf@1.1.2:
    obuf: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  ora@4.1.1:
    ora: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@3.0.0:
    p-map: private
  p-try@1.0.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  packages/ai:
    '@repo/ai': private
  packages/analytics:
    '@repo/analytics': private
  packages/auth:
    '@repo/auth': private
  packages/cms:
    '@repo/cms': private
  packages/collaboration:
    '@repo/collaboration': private
  packages/database:
    '@repo/database': private
  packages/design-system:
    '@repo/design-system': private
  packages/email:
    '@repo/email': private
  packages/feature-flags:
    '@repo/feature-flags': private
  packages/internationalization:
    '@repo/internationalization': private
  packages/next-config:
    '@repo/next-config': private
  packages/notifications:
    '@repo/notifications': private
  packages/observability:
    '@repo/observability': private
  packages/payments:
    '@repo/payments': private
  packages/rate-limit:
    '@repo/rate-limit': private
  packages/security:
    '@repo/security': private
  packages/seo:
    '@repo/seo': private
  packages/storage:
    '@repo/storage': private
  packages/testing:
    '@repo/testing': private
  packages/webhooks:
    '@repo/webhooks': private
  param-case@2.1.1:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  parse-author@2.0.0:
    parse-author: private
  parse-github-url@1.0.2:
    parse-github-url: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@2.1.0:
    parse-ms: private
  parse5@7.2.1:
    parse5: private
  pascal-case@2.0.1:
    pascal-case: private
  path-case@2.1.1:
    path-case: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-numeric@1.0.2:
    pg-numeric: private
  pg-protocol@1.7.1:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pify@3.0.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  pkg-conf@2.1.0:
    pkg-conf: private
  pkg-types@1.3.1:
    pkg-types: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-load-config@6.0.1(jiti@2.4.2)(postcss@8.5.3)(yaml@2.7.0):
    postcss-load-config: private
  postcss@8.4.31:
    postcss: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  postgres-range@1.1.4:
    postgres-range: private
  pretty-format@27.5.1:
    pretty-format: private
  pretty-ms@7.0.1:
    pretty-ms: private
  progress@2.0.3:
    progress: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  rc@1.2.8:
    rc: private
  react-dom@19.1.0(react@19.1.0):
    react-dom: private
  react-is@17.0.2:
    react-is: private
  react@19.1.0:
    react: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  registry-auth-token@3.3.2:
    registry-auth-token: private
  registry-url@3.1.0:
    registry-url: private
  require-from-string@2.0.2:
    require-from-string: private
  require-in-the-middle@7.5.2:
    require-in-the-middle: private
  requireg@0.2.2:
    requireg: private
  requires-port@1.0.0:
    requires-port: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve@1.22.8:
    resolve: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.35.0:
    rollup: private
  rrweb-cssom@0.8.0:
    rrweb-cssom: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  rxjs@7.8.2:
    rxjs: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  saxes@6.0.0:
    saxes: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@4.3.0:
    schema-utils: private
  semver@7.7.1:
    semver: private
  sentence-case@2.1.1:
    sentence-case: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  server-only@0.0.1:
    server-only: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.34.2:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  signale@1.4.0:
    signale: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  smart-buffer@4.2.0:
    smart-buffer: private
  snake-case@2.1.0:
    snake-case: private
  snakecase-keys@8.0.1:
    snakecase-keys: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  sonner@2.0.3(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    sonner: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.8.0-beta.0:
    source-map: private
  sprintf-js@1.1.3:
    sprintf-js: private
  stackback@0.0.2:
    stackback: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  std-env@3.9.0:
    std-env: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@2.0.1:
    strip-json-comments: private
  styled-jsx@5.1.6(@babel/core@7.26.9)(react@19.1.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@2.3.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svix-fetch@3.0.0:
    svix-fetch: private
  svix@1.66.0:
    svix: private
  swap-case@1.1.2:
    swap-case: private
  swr@2.3.3(react@19.1.0):
    swr: private
  symbol-tree@3.2.4:
    symbol-tree: private
  tailwindcss@4.1.7:
    tailwindcss: private
  tapable@2.2.1:
    tapable: private
  terminal-link@2.1.1:
    terminal-link: private
  terser-webpack-plugin@5.3.13(esbuild@0.25.2)(webpack@5.98.0(esbuild@0.25.2)):
    terser-webpack-plugin: private
  terser@5.39.0:
    terser: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  through@2.3.8:
    through: private
  tinybench@2.9.0:
    tinybench: private
  tinycolor2@1.6.0:
    tinycolor2: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.12:
    tinyglobby: private
  tinygradient@1.1.5:
    tinygradient: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  title-case@2.1.1:
    title-case: private
  tldts-core@6.1.82:
    tldts-core: private
  tldts@6.1.82:
    tldts: private
  tmp@0.0.33:
    tmp: private
  to-regex-range@5.0.1:
    to-regex-range: private
  tough-cookie@5.1.2:
    tough-cookie: private
  tr46@5.0.0:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-node@10.9.2(@types/node@22.15.21)(typescript@5.8.3):
    ts-node: private
  tslib@2.1.0:
    tslib: private
  turbo-darwin-64@2.5.3:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.3:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.3:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.3:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.3:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.3:
    turbo-windows-arm64: private
  type-fest@0.21.3:
    type-fest: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typescript-memoize@1.1.1:
    typescript-memoize: private
  ufo@1.6.1:
    ufo: private
  uglify-js@3.19.3:
    uglify-js: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  universal-user-agent@6.0.1:
    universal-user-agent: private
  universalify@2.0.1:
    universalify: private
  unplugin@1.0.1:
    unplugin: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  update-check@1.5.4:
    update-check: private
  upper-case-first@1.1.2:
    upper-case-first: private
  upper-case@1.1.3:
    upper-case: private
  url-join@4.0.1:
    url-join: private
  url-parse@1.5.10:
    url-parse: private
  use-sync-external-store@1.4.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@9.0.1:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  validate-npm-package-name@5.0.1:
    validate-npm-package-name: private
  vite-node@3.1.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.39.0)(yaml@2.7.0):
    vite-node: private
  vite@6.2.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(terser@5.39.0)(yaml@2.7.0):
    vite: private
  w3c-xmlserializer@5.0.0:
    w3c-xmlserializer: private
  watchpack@2.4.2:
    watchpack: private
  wcwidth@1.0.1:
    wcwidth: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-sources@3.2.3:
    webpack-sources: private
  webpack-virtual-modules@0.5.0:
    webpack-virtual-modules: private
  webpack@5.98.0(esbuild@0.25.2):
    webpack: private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-fetch@3.6.20:
    whatwg-fetch: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@14.1.1:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.18:
    which-typed-array: private
  which@2.0.2:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wordwrap@1.0.0:
    wordwrap: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.1(bufferutil@4.0.9):
    ws: private
  xml-name-validator@5.0.0:
    xml-name-validator: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  yallist@3.1.1:
    yallist: private
  yaml@2.7.0:
    yaml: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod@3.25.28:
    zod: private
ignoredBuilds:
  - '@biomejs/biome'
  - core-js-pure
  - esbuild
  - bufferutil
  - '@sentry/cli'
  - sharp
  - '@prisma/engines'
  - core-js
  - '@clerk/shared'
  - '@tailwindcss/oxide'
  - prisma
  - '@prisma/client'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Fri, 20 Jun 2025 23:23:17 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@biomejs/cli-darwin-arm64@1.9.4'
  - '@biomejs/cli-darwin-x64@1.9.4'
  - '@biomejs/cli-linux-arm64-musl@1.9.4'
  - '@biomejs/cli-linux-arm64@1.9.4'
  - '@biomejs/cli-linux-x64-musl@1.9.4'
  - '@biomejs/cli-linux-x64@1.9.4'
  - '@biomejs/cli-win32-arm64@1.9.4'
  - '@emnapi/runtime@1.4.1'
  - '@emnapi/runtime@1.4.3'
  - '@esbuild/aix-ppc64@0.25.2'
  - '@esbuild/android-arm64@0.19.2'
  - '@esbuild/android-arm64@0.25.2'
  - '@esbuild/android-arm@0.19.2'
  - '@esbuild/android-arm@0.25.2'
  - '@esbuild/android-x64@0.19.2'
  - '@esbuild/android-x64@0.25.2'
  - '@esbuild/darwin-arm64@0.19.2'
  - '@esbuild/darwin-arm64@0.25.2'
  - '@esbuild/darwin-x64@0.19.2'
  - '@esbuild/darwin-x64@0.25.2'
  - '@esbuild/freebsd-arm64@0.19.2'
  - '@esbuild/freebsd-arm64@0.25.2'
  - '@esbuild/freebsd-x64@0.19.2'
  - '@esbuild/freebsd-x64@0.25.2'
  - '@esbuild/linux-arm64@0.19.2'
  - '@esbuild/linux-arm64@0.25.2'
  - '@esbuild/linux-arm@0.19.2'
  - '@esbuild/linux-arm@0.25.2'
  - '@esbuild/linux-ia32@0.19.2'
  - '@esbuild/linux-ia32@0.25.2'
  - '@esbuild/linux-loong64@0.19.2'
  - '@esbuild/linux-loong64@0.25.2'
  - '@esbuild/linux-mips64el@0.19.2'
  - '@esbuild/linux-mips64el@0.25.2'
  - '@esbuild/linux-ppc64@0.19.2'
  - '@esbuild/linux-ppc64@0.25.2'
  - '@esbuild/linux-riscv64@0.19.2'
  - '@esbuild/linux-riscv64@0.25.2'
  - '@esbuild/linux-s390x@0.19.2'
  - '@esbuild/linux-s390x@0.25.2'
  - '@esbuild/linux-x64@0.19.2'
  - '@esbuild/linux-x64@0.25.2'
  - '@esbuild/netbsd-arm64@0.25.2'
  - '@esbuild/netbsd-x64@0.19.2'
  - '@esbuild/netbsd-x64@0.25.2'
  - '@esbuild/openbsd-arm64@0.25.2'
  - '@esbuild/openbsd-x64@0.19.2'
  - '@esbuild/openbsd-x64@0.25.2'
  - '@esbuild/sunos-x64@0.19.2'
  - '@esbuild/sunos-x64@0.25.2'
  - '@esbuild/win32-arm64@0.19.2'
  - '@esbuild/win32-arm64@0.25.2'
  - '@esbuild/win32-ia32@0.19.2'
  - '@esbuild/win32-ia32@0.25.2'
  - '@img/sharp-darwin-arm64@0.33.5'
  - '@img/sharp-darwin-arm64@0.34.2'
  - '@img/sharp-darwin-x64@0.33.5'
  - '@img/sharp-darwin-x64@0.34.2'
  - '@img/sharp-libvips-darwin-arm64@1.0.4'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.0.4'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.0.4'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.0.5'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.0.4'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.0.4'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.0.4'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.33.5'
  - '@img/sharp-linux-arm64@0.34.2'
  - '@img/sharp-linux-arm@0.33.5'
  - '@img/sharp-linux-arm@0.34.2'
  - '@img/sharp-linux-s390x@0.33.5'
  - '@img/sharp-linux-s390x@0.34.2'
  - '@img/sharp-linux-x64@0.33.5'
  - '@img/sharp-linux-x64@0.34.2'
  - '@img/sharp-linuxmusl-arm64@0.33.5'
  - '@img/sharp-linuxmusl-arm64@0.34.2'
  - '@img/sharp-linuxmusl-x64@0.33.5'
  - '@img/sharp-linuxmusl-x64@0.34.2'
  - '@img/sharp-wasm32@0.33.5'
  - '@img/sharp-wasm32@0.34.2'
  - '@img/sharp-win32-arm64@0.34.2'
  - '@img/sharp-win32-ia32@0.33.5'
  - '@img/sharp-win32-ia32@0.34.2'
  - '@next/swc-darwin-arm64@15.1.7'
  - '@next/swc-darwin-arm64@15.3.2'
  - '@next/swc-darwin-x64@15.1.7'
  - '@next/swc-darwin-x64@15.3.2'
  - '@next/swc-linux-arm64-gnu@15.1.7'
  - '@next/swc-linux-arm64-gnu@15.3.2'
  - '@next/swc-linux-arm64-musl@15.1.7'
  - '@next/swc-linux-arm64-musl@15.3.2'
  - '@next/swc-linux-x64-gnu@15.1.7'
  - '@next/swc-linux-x64-gnu@15.3.2'
  - '@next/swc-linux-x64-musl@15.1.7'
  - '@next/swc-linux-x64-musl@15.3.2'
  - '@next/swc-win32-arm64-msvc@15.1.7'
  - '@next/swc-win32-arm64-msvc@15.3.2'
  - '@rollup/rollup-android-arm-eabi@4.35.0'
  - '@rollup/rollup-android-arm-eabi@4.38.0'
  - '@rollup/rollup-android-arm64@4.35.0'
  - '@rollup/rollup-android-arm64@4.38.0'
  - '@rollup/rollup-darwin-arm64@4.35.0'
  - '@rollup/rollup-darwin-arm64@4.38.0'
  - '@rollup/rollup-darwin-x64@4.35.0'
  - '@rollup/rollup-darwin-x64@4.38.0'
  - '@rollup/rollup-freebsd-arm64@4.35.0'
  - '@rollup/rollup-freebsd-arm64@4.38.0'
  - '@rollup/rollup-freebsd-x64@4.35.0'
  - '@rollup/rollup-freebsd-x64@4.38.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.35.0'
  - '@rollup/rollup-linux-arm-gnueabihf@4.38.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.35.0'
  - '@rollup/rollup-linux-arm-musleabihf@4.38.0'
  - '@rollup/rollup-linux-arm64-gnu@4.35.0'
  - '@rollup/rollup-linux-arm64-gnu@4.38.0'
  - '@rollup/rollup-linux-arm64-musl@4.35.0'
  - '@rollup/rollup-linux-arm64-musl@4.38.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.35.0'
  - '@rollup/rollup-linux-loongarch64-gnu@4.38.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.35.0'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.38.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.35.0'
  - '@rollup/rollup-linux-riscv64-gnu@4.38.0'
  - '@rollup/rollup-linux-riscv64-musl@4.38.0'
  - '@rollup/rollup-linux-s390x-gnu@4.35.0'
  - '@rollup/rollup-linux-s390x-gnu@4.38.0'
  - '@rollup/rollup-linux-x64-gnu@4.35.0'
  - '@rollup/rollup-linux-x64-gnu@4.38.0'
  - '@rollup/rollup-linux-x64-musl@4.35.0'
  - '@rollup/rollup-linux-x64-musl@4.38.0'
  - '@rollup/rollup-win32-arm64-msvc@4.35.0'
  - '@rollup/rollup-win32-arm64-msvc@4.38.0'
  - '@rollup/rollup-win32-ia32-msvc@4.35.0'
  - '@rollup/rollup-win32-ia32-msvc@4.38.0'
  - '@sentry/cli-darwin@2.42.2'
  - '@sentry/cli-linux-arm64@2.42.2'
  - '@sentry/cli-linux-arm@2.42.2'
  - '@sentry/cli-linux-i686@2.42.2'
  - '@sentry/cli-linux-x64@2.42.2'
  - '@sentry/cli-win32-i686@2.42.2'
  - '@tailwindcss/oxide-android-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-arm64@4.1.7'
  - '@tailwindcss/oxide-darwin-x64@4.1.7'
  - '@tailwindcss/oxide-freebsd-x64@4.1.7'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.7'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.7'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.7'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.7'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.7'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.30.1
  - lightningcss-darwin-x64@1.30.1
  - lightningcss-freebsd-x64@1.30.1
  - lightningcss-linux-arm-gnueabihf@1.30.1
  - lightningcss-linux-arm64-gnu@1.30.1
  - lightningcss-linux-arm64-musl@1.30.1
  - lightningcss-linux-x64-gnu@1.30.1
  - lightningcss-linux-x64-musl@1.30.1
  - lightningcss-win32-arm64-msvc@1.30.1
  - turbo-darwin-64@2.5.3
  - turbo-darwin-arm64@2.5.3
  - turbo-linux-64@2.5.3
  - turbo-linux-arm64@2.5.3
  - turbo-windows-arm64@2.5.3
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubentweb\cubent\node_modules\.pnpm
virtualStoreDirMaxLength: 60
